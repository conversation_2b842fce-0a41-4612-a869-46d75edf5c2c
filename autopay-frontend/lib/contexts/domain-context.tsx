'use client'

import React, { createContext, useEffect } from 'react'
import { useMetaTags } from 'react-metatags-hook'
import type { DomainConfig, DomainContextType } from '../types/domain'
import { resetApiUrl, setGlobalApiUrl } from '../utils/fetchHelper'

export const DomainContext = createContext<DomainContextType | null>(null)

interface DomainProviderProps {
  children: React.ReactNode
  initialConfig?: DomainConfig | null
}

export function DomainProvider({ children, initialConfig }: DomainProviderProps): React.JSX.Element {
  // Use initialConfig directly since we only rely on SSR now
  const config = initialConfig || null

  // Use meta tags hook for SEO
  useMetaTags(
    {
      title: config?.seo?.title || '',
      description: config?.seo?.description || '',
      metas: [...(config?.seo?.keywords ? [{ name: 'keywords', content: config.seo.keywords }] : [])],
      links: [...(config?.branding?.favicon_url ? [{ rel: 'icon', href: config.branding.favicon_url }] : [])],
      openGraph: {
        ...(config?.seo?.og_image ? { image: config.seo.og_image } : {}),
        ...(config?.seo?.title ? { title: config.seo.title } : {}),
        ...(config?.seo?.description ? { description: config.seo.description } : {}),
      },
    },
    [config?.seo, config?.branding?.favicon_url]
  )

  // Update global API URL when config changes
  useEffect(() => {
    if (config?.backend_hostname) {
      setGlobalApiUrl(`https://${config.backend_hostname}`)
    } else {
      resetApiUrl()
    }
  }, [config?.backend_hostname])

  // Apply theme CSS variables when config changes
  useEffect(() => {
    if (config?.theme) {
      const root = document.documentElement

      // Apply theme colors as CSS custom properties with higher priority
      Object.entries(config.theme).forEach(([key, value]) => {
        // Set domain-specific variables
        root.style.setProperty(`--domain-${key}`, value)

        // Override default CSS variables with !important for higher priority
        switch (key) {
          case 'primary':
            root.style.setProperty('--primary', value)
            root.style.setProperty('--color-primary', value)
            break
          case 'secondary':
            root.style.setProperty('--secondary', value)
            root.style.setProperty('--color-secondary', value)
            break
          case 'accent':
            root.style.setProperty('--accent', value)
            root.style.setProperty('--color-accent', value)
            break
          case 'background':
            root.style.setProperty('--background', value)
            root.style.setProperty('--color-background', value)
            break
          case 'surface':
            root.style.setProperty('--card', value)
            root.style.setProperty('--color-card', value)
            break
          case 'text':
            root.style.setProperty('--foreground', value)
            root.style.setProperty('--color-foreground', value)
            break
          case 'text_secondary':
            root.style.setProperty('--muted-foreground', value)
            root.style.setProperty('--color-muted-foreground', value)
            break
        }
      })

      // Apply custom CSS with high specificity
      if (config.custom_css && Object.keys(config.custom_css).length > 0) {
        const styleId = 'domain-custom-styles'
        let styleElement = document.getElementById(styleId) as HTMLStyleElement

        if (!styleElement) {
          styleElement = document.createElement('style')
          styleElement.id = styleId
          // Add to head with high priority
          document.head.appendChild(styleElement)
        }

        // Convert custom CSS object to CSS string with higher specificity
        const cssString = Object.entries(config.custom_css)
          .map(([selector, styles]) => {
            if (typeof styles === 'object') {
              const styleProps = Object.entries(styles as Record<string, string>)
                .map(([prop, value]) => {
                  // Add !important to ensure override
                  const hasImportant = value.includes('!important')
                  return `${prop}: ${value}${hasImportant ? '' : ' !important'};`
                })
                .join(' ')

              // Increase specificity by adding body prefix and data attribute
              const specificSelector = selector.startsWith(':root') ? selector : `body[data-domain-custom] ${selector}`

              return `${specificSelector} { ${styleProps} }`
            }
            return ''
          })
          .join('\n')

        styleElement.textContent = cssString

        // Add data attribute to body for CSS targeting
        document.body.setAttribute('data-domain-custom', 'true')
      }
    }

    // Cleanup function to remove custom styles when config changes
    return () => {
      const styleElement = document.getElementById('domain-custom-styles')
      if (styleElement && !config?.custom_css) {
        styleElement.remove()
        document.body.removeAttribute('data-domain-custom')
      }
    }
  }, [config])

  const contextValue: DomainContextType = {
    config,
  }

  return <DomainContext.Provider value={contextValue}>{children}</DomainContext.Provider>
}
