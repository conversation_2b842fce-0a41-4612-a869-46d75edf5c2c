/**
 * Utility functions for CSS override and theme management
 */

export interface ThemeColors {
  primary: string
  secondary: string
  accent: string
  background: string
  surface: string
  text: string
  text_secondary: string
}

export interface CustomCSSRule {
  selector: string
  properties: Record<string, string>
}

/**
 * Apply theme colors with proper CSS variable mapping
 */
export function applyThemeColors(colors: ThemeColors): void {
  const root = document.documentElement

  Object.entries(colors).forEach(([key, value]) => {
    // Set domain-specific variables
    root.style.setProperty(`--domain-${key}`, value)

    // Map to standard CSS variables used by the design system
    const mappings: Record<string, string[]> = {
      primary: ['--primary', '--color-primary', '--sidebar-primary'],
      secondary: ['--secondary', '--color-secondary'],
      accent: ['--accent', '--color-accent'],
      background: ['--background', '--color-background'],
      surface: ['--card', '--color-card', '--popover'],
      text: ['--foreground', '--color-foreground'],
      text_secondary: ['--muted-foreground', '--color-muted-foreground'],
    }

    const cssVars = mappings[key] || []
    cssVars.forEach((cssVar) => {
      root.style.setProperty(cssVar, value)
    })
  })
}

/**
 * Generate CSS string from custom CSS rules with high specificity
 */
export function generateCustomCSS(customCSS: Record<string, any>): string {
  return Object.entries(customCSS)
    .map(([selector, styles]) => {
      if (typeof styles === 'object') {
        const styleProps = Object.entries(styles as Record<string, string>)
          .map(([prop, value]) => {
            // Add !important to ensure override unless already present
            const hasImportant = value.includes('!important')
            return `${prop}: ${value}${hasImportant ? '' : ' !important'};`
          })
          .join(' ')

        // Increase specificity for better override capability
        const specificSelector = getHighSpecificitySelector(selector)

        return `${specificSelector} { ${styleProps} }`
      }
      return ''
    })
    .filter(Boolean)
    .join('\n')
}

/**
 * Get high specificity selector for better CSS override
 */
function getHighSpecificitySelector(selector: string): string {
  // Don't modify :root selectors
  if (selector.startsWith(':root')) {
    return selector
  }

  // Don't modify selectors that already have high specificity
  if (selector.includes('body[data-domain-custom]')) {
    return selector
  }

  // Add body prefix with data attribute for higher specificity
  return `body[data-domain-custom] ${selector}`
}

/**
 * Apply custom CSS with proper cleanup and specificity
 */
export function applyCustomCSS(customCSS: Record<string, any>): () => void {
  const styleId = 'domain-custom-styles'
  let styleElement = document.getElementById(styleId) as HTMLStyleElement

  if (!styleElement) {
    styleElement = document.createElement('style')
    styleElement.id = styleId
    // Insert at the end of head for higher priority
    document.head.appendChild(styleElement)
  }

  const cssString = generateCustomCSS(customCSS)
  styleElement.textContent = cssString

  // Add data attribute to body for CSS targeting
  document.body.setAttribute('data-domain-custom', 'true')

  // Return cleanup function
  return () => {
    const element = document.getElementById(styleId)
    if (element) {
      element.remove()
    }
    document.body.removeAttribute('data-domain-custom')
  }
}

/**
 * Remove all domain-specific styles
 */
export function removeCustomStyles(): void {
  const styleElement = document.getElementById('domain-custom-styles')
  if (styleElement) {
    styleElement.remove()
  }
  document.body.removeAttribute('data-domain-custom')

  // Reset CSS variables to defaults
  const root = document.documentElement
  const domainVars = Array.from(root.style).filter((prop) => prop.startsWith('--domain-'))
  domainVars.forEach((prop) => {
    root.style.removeProperty(prop)
  })
}

/**
 * Validate CSS color value
 */
export function isValidColor(color: string): boolean {
  const style = new Option().style
  style.color = color
  return style.color !== ''
}

/**
 * Convert hex color to CSS custom property format
 */
export function hexToCustomProperty(hex: string): string {
  // Remove # if present
  const cleanHex = hex.replace('#', '')

  // Convert to RGB values
  const r = parseInt(cleanHex.substring(0, 2), 16)
  const g = parseInt(cleanHex.substring(2, 4), 16)
  const b = parseInt(cleanHex.substring(4, 6), 16)

  // Return as oklch format (preferred by the design system)
  return `rgb(${r}, ${g}, ${b})`
}

/**
 * Get computed CSS variable value
 */
export function getCSSVariable(variable: string): string {
  return getComputedStyle(document.documentElement).getPropertyValue(variable).trim()
}

/**
 * Set CSS variable with fallback
 */
export function setCSSVariable(variable: string, value: string, fallback?: string): void {
  const root = document.documentElement

  if (isValidColor(value)) {
    root.style.setProperty(variable, value)
  } else if (fallback && isValidColor(fallback)) {
    root.style.setProperty(variable, fallback)
  }
}
