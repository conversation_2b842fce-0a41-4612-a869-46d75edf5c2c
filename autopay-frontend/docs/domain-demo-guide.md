# Domain Customization Demo Guide

## Tổng quan

Demo này cho phép bạn test và trải nghiệm tính năng tùy chỉnh giao diện cho domain trong hệ thống multi-tenant. <PERSON><PERSON><PERSON> có thể thay đổi màu sắc và CSS tùy chỉnh để xem cách chúng ảnh hưởng đến giao diện.

## Cách truy cập Demo

1. Chạy development server:
   ```bash
   cd autopay-frontend
   npm run dev
   ```

2. Mở browser và truy cập:
   ```
   http://localhost:3000/settings/domain-demo
   ```

3. Hoặc từ trang Settings, click vào tab "Demo Domain" trong sidebar

## Tính năng Demo

### 1. Color Picker
- **Primary Color**: Màu chính của domain (buttons, links, highlights)
- **Secondary Color**: <PERSON><PERSON><PERSON> phụ (secondary buttons, borders)
- **Accent Color**: <PERSON><PERSON><PERSON> <PERSON> (badges, notifications, special elements)
- **Background**: <PERSON><PERSON><PERSON> nề<PERSON> chính
- **Surface**: <PERSON><PERSON>u nền cho cards và surfaces
- **Text**: <PERSON><PERSON><PERSON> chữ chính
- **Text Secondary**: <PERSON><PERSON><PERSON> chữ phụ

### 2. Custom CSS Editor
Cho phép thêm CSS tùy chỉnh với format JSON:
```json
{
  ".demo-button": {
    "border-radius": "12px",
    "font-weight": "600",
    "text-transform": "uppercase"
  },
  ".demo-card": {
    "border": "2px solid var(--domain-accent)",
    "box-shadow": "0 8px 16px rgba(0, 0, 0, 0.1)"
  }
}
```

### 3. Live Preview
Tất cả thay đổi được áp dụng ngay lập tức trên các demo elements:
- **Navigation Bar**: Với gradient background
- **Buttons**: Các loại button với custom styling
- **Form Elements**: Input và controls
- **Color Palette**: Hiển thị bảng màu hiện tại
- **Cards Grid**: Demo cards với styling tùy chỉnh

## Cách sử dụng

### Thay đổi màu sắc:
1. Click vào color picker cho màu bạn muốn thay đổi
2. Chọn màu mới từ color picker
3. Màu sắc sẽ được áp dụng ngay lập tức trên demo elements

### Thêm CSS tùy chỉnh:
1. Mở tab "Custom CSS" trong DomainColorPicker
2. Thêm CSS rules theo format JSON
3. Sử dụng CSS variables như `var(--domain-primary)` để tham chiếu màu domain
4. CSS sẽ được áp dụng ngay lập tức

### CSS Variables có sẵn:
- `--domain-primary`
- `--domain-secondary` 
- `--domain-accent`
- `--domain-background`
- `--domain-surface`
- `--domain-text`
- `--domain-text-secondary`

### CSS Classes có sẵn:
- `.domain-bg-primary`, `.domain-bg-secondary`, `.domain-bg-accent`
- `.domain-border-primary`, `.domain-border-secondary`, `.domain-border-accent`
- `.domain-primary`, `.domain-secondary`, `.domain-accent` (text colors)

## Ví dụ CSS tùy chỉnh

### Button styling:
```json
{
  ".demo-button": {
    "border-radius": "20px",
    "font-weight": "700",
    "text-transform": "uppercase",
    "letter-spacing": "1px",
    "transition": "all 0.3s ease"
  }
}
```

### Card styling:
```json
{
  ".demo-card": {
    "border": "3px solid var(--domain-primary)",
    "border-radius": "20px",
    "box-shadow": "0 10px 30px rgba(0, 0, 0, 0.15)",
    "background": "linear-gradient(135deg, var(--domain-surface), white)"
  }
}
```

### Navbar styling:
```json
{
  ".demo-navbar": {
    "background": "linear-gradient(45deg, var(--domain-primary), var(--domain-accent))",
    "border-radius": "15px",
    "box-shadow": "0 5px 15px rgba(0, 0, 0, 0.2)"
  }
}
```

## Lưu cấu hình

1. Click button "Lưu cấu hình" để simulate việc lưu
2. Cấu hình sẽ được hiển thị trong toast notification
3. Trong production, dữ liệu sẽ được gửi đến API backend

## Reset về mặc định

Click button "Đặt lại" để reset tất cả cấu hình về giá trị mặc định.

## Lưu ý kỹ thuật

- Demo sử dụng CSS injection để áp dụng styles với high specificity
- CSS variables được inject vào document root
- Custom CSS được validate và sanitize trước khi áp dụng
- Tất cả thay đổi chỉ ảnh hưởng đến session hiện tại

## Troubleshooting

### CSS không được áp dụng:
- Kiểm tra format JSON có đúng không
- Đảm bảo CSS selectors hợp lệ
- Kiểm tra console để xem có lỗi JavaScript không

### Màu sắc không thay đổi:
- Refresh trang và thử lại
- Kiểm tra CSS classes có được áp dụng đúng không
- Đảm bảo elements có classes tương ứng

### Performance issues:
- Tránh CSS rules quá phức tạp
- Limit số lượng custom CSS rules
- Sử dụng CSS variables thay vì hardcode values
