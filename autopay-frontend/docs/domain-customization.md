# Domain Customization Guide

Hướng dẫn tùy chỉnh giao diện cho từng domain trong hệ thống multi-tenant.

## Tổng quan

Hệ thống hỗ trợ tùy chỉnh giao diện cho từng organization thông qua:
- **Theme Colors**: <PERSON><PERSON><PERSON> sắc chủ đề (primary, secondary, accent, etc.)
- **Custom CSS**: CSS tùy chỉnh với độ ưu tiên cao để ghi đè styles mặc định

## Cơ chế hoạt động

### 1. CSS Variable Override
```css
/* Hệ thống tự động map theme colors thành CSS variables */
--domain-primary: #3b82f6
--primary: #3b82f6          /* Override default */
--color-primary: #3b82f6    /* Tailwind compatibility */
```

### 2. CSS Specificity Enhancement
```css
/* Custom CSS được tăng độ ưu tiên tự động */
body[data-domain-custom] .btn-primary {
  background-color: var(--domain-primary) !important;
}
```

### 3. Automatic !important Addition
```json
{
  ".navbar": {
    "background-color": "#ff0000"  // Tự động thêm !important
  }
}
```

## Cấu hình Theme Colors

### Các màu có sẵn:
- `primary`: Màu chính (buttons, links, highlights)
- `secondary`: Màu phụ (secondary buttons, borders)
- `accent`: Màu nhấn (badges, notifications)
- `background`: Màu nền chính
- `surface`: Màu bề mặt (cards, modals)
- `text`: Màu chữ chính
- `text_secondary`: Màu chữ phụ

### Ví dụ cấu hình:
```json
{
  "primary": "#ff6b35",
  "secondary": "#004e89",
  "accent": "#ffa500",
  "background": "#ffffff",
  "surface": "#f8f9fa",
  "text": "#212529",
  "text_secondary": "#6c757d"
}
```

## Custom CSS

### Định dạng JSON:
```json
{
  "selector": {
    "property": "value"
  }
}
```

### Ví dụ thực tế:
```json
{
  ".navbar": {
    "background": "linear-gradient(45deg, var(--domain-primary), var(--domain-secondary))",
    "box-shadow": "0 2px 4px rgba(0,0,0,0.1)"
  },
  ".btn-primary": {
    "border-radius": "25px",
    "font-weight": "600",
    "text-transform": "uppercase"
  },
  ".card": {
    "border": "2px solid var(--domain-accent)",
    "border-radius": "12px"
  },
  ":root": {
    "--custom-spacing": "2rem",
    "--custom-font-size": "1.1rem"
  }
}
```

## CSS Variables có sẵn

### Domain-specific variables:
- `--domain-primary`
- `--domain-secondary`
- `--domain-accent`
- `--domain-background`
- `--domain-surface`
- `--domain-text`
- `--domain-text_secondary`

### System variables (được override tự động):
- `--primary`, `--color-primary`
- `--secondary`, `--color-secondary`
- `--accent`, `--color-accent`
- `--background`, `--color-background`
- `--card`, `--color-card`
- `--foreground`, `--color-foreground`
- `--muted-foreground`, `--color-muted-foreground`

## Utility Classes

### Domain color utilities:
```css
.domain-primary          /* color: var(--domain-primary) */
.domain-bg-primary       /* background-color: var(--domain-primary) */
.domain-border-primary   /* border-color: var(--domain-primary) */

.domain-secondary        /* color: var(--domain-secondary) */
.domain-bg-secondary     /* background-color: var(--domain-secondary) */
.domain-border-secondary /* border-color: var(--domain-secondary) */

.domain-accent           /* color: var(--domain-accent) */
.domain-bg-accent        /* background-color: var(--domain-accent) */
.domain-border-accent    /* border-color: var(--domain-accent) */
```

### Sử dụng trong component:
```tsx
<Button className="domain-bg-primary domain-border-primary">
  Custom Button
</Button>
```

## Best Practices

### 1. Sử dụng CSS Variables
```json
{
  ".custom-element": {
    "color": "var(--domain-primary)",
    "background": "var(--domain-surface)",
    "border": "1px solid var(--domain-secondary)"
  }
}
```

### 2. Responsive Design
```json
{
  ".navbar": {
    "padding": "1rem"
  },
  "@media (max-width: 768px)": {
    ".navbar": {
      "padding": "0.5rem"
    }
  }
}
```

### 3. Hover Effects
```json
{
  ".btn-custom": {
    "background-color": "var(--domain-primary)",
    "transition": "all 0.3s ease"
  },
  ".btn-custom:hover": {
    "background-color": "var(--domain-secondary)",
    "transform": "translateY(-2px)"
  }
}
```

### 4. Dark Mode Support
```json
{
  ":root": {
    "--custom-bg": "var(--domain-background)"
  },
  ".dark": {
    "--custom-bg": "var(--domain-surface)"
  },
  ".custom-container": {
    "background-color": "var(--custom-bg)"
  }
}
```

## Troubleshooting

### CSS không được áp dụng:
1. Kiểm tra JSON syntax có hợp lệ không
2. Đảm bảo selector đúng định dạng
3. Kiểm tra console browser có lỗi không

### Màu không hiển thị:
1. Kiểm tra format màu (hex, rgb, hsl)
2. Đảm bảo CSS variable được định nghĩa
3. Kiểm tra độ ưu tiên CSS

### Performance:
1. Tránh sử dụng quá nhiều custom CSS
2. Sử dụng CSS variables thay vì hardcode values
3. Optimize selectors để tránh reflow

## API Integration

### Backend structure:
```php
// Domain model
'theme_colors' => 'array',  // JSON field
'custom_css' => 'array',    // JSON field
```

### Frontend usage:
```tsx
import { DomainColorPicker } from '@/components/domain/domain-color-picker'

<DomainColorPicker
  initialColors={domain.theme_colors}
  initialCustomCSS={domain.custom_css}
  onColorsChange={handleColorsChange}
  onCustomCSSChange={handleCSSChange}
  showPreview={true}
/>
```
