# Domain Customization Demo Script

## Demo Scenario: Tùy chỉnh giao diện cho domain "example.com"

### Bước 1: <PERSON><PERSON><PERSON> cập Demo (30 giây)
1. Mở browser và truy cập `http://localhost:3000/settings/domain-demo`
2. G<PERSON><PERSON>i thích giao diện demo:
   - **Header**: Tiêu đề và các button action
   - **Domain Color Picker**: Tool chính để tùy chỉnh
   - **Demo Elements**: Các element để xem thay đổi real-time

### Bước 2: Demo thay đổi màu sắc (2 phút)

#### Thay đổi Primary Color:
1. Click vào color picker "Primary Color"
2. Chọn màu xanh lá: `#10b981` (green-500)
3. **Quan sát**: 
   - Primary buttons chuyển sang màu xanh lá
   - Navigation bar gradient thay đổi
   - Color palette cập nhật

#### Thay đổi Accent Color:
1. Click vào color picker "Accent Color"  
2. Chọn màu cam: `#f97316` (orange-500)
3. **Quan sát**:
   - <PERSON><PERSON> chuyển sang màu cam
   - Demo cards border thay đổi
   - Small buttons cập nhật

#### Thay đổi Secondary Color:
1. Click vào color picker "Secondary Color"
2. Chọn màu tím: `#8b5cf6` (violet-500)
3. **Quan sát**:
   - Secondary buttons thay đổi
   - Navigation gradient cập nhật

### Bước 3: Demo Custom CSS (2 phút)

#### Mở Custom CSS Editor:
1. Click tab "Custom CSS" trong DomainColorPicker
2. Giải thích format JSON hiện tại

#### Thêm Button Animation:
```json
{
  ".demo-button": {
    "border-radius": "25px",
    "font-weight": "700",
    "text-transform": "uppercase",
    "letter-spacing": "1px",
    "transition": "all 0.3s ease",
    "transform": "scale(1)",
    "box-shadow": "0 4px 15px rgba(0, 0, 0, 0.2)"
  },
  ".demo-button:hover": {
    "transform": "scale(1.05)",
    "box-shadow": "0 6px 20px rgba(0, 0, 0, 0.3)"
  }
}
```

#### Thêm Card Styling:
```json
{
  ".demo-card": {
    "border": "3px solid var(--domain-primary)",
    "border-radius": "20px",
    "box-shadow": "0 15px 35px rgba(0, 0, 0, 0.1)",
    "background": "linear-gradient(135deg, var(--domain-surface), white)",
    "transition": "transform 0.3s ease"
  },
  ".demo-card:hover": {
    "transform": "translateY(-5px)",
    "box-shadow": "0 20px 40px rgba(0, 0, 0, 0.15)"
  }
}
```

#### Thêm Navbar Styling:
```json
{
  ".demo-navbar": {
    "background": "linear-gradient(45deg, var(--domain-primary), var(--domain-accent))",
    "border-radius": "20px",
    "box-shadow": "0 8px 25px rgba(0, 0, 0, 0.2)",
    "border": "2px solid rgba(255, 255, 255, 0.2)"
  }
}
```

### Bước 4: Demo Live Preview (1 phút)

#### Quan sát thay đổi real-time:
1. **Buttons**: Rounded corners, hover effects, shadows
2. **Cards**: Border colors, hover animations, gradients  
3. **Navbar**: Gradient background, rounded corners
4. **Color Palette**: Cập nhật màu sắc mới

#### Test responsive:
1. Resize browser window
2. Quan sát layout responsive
3. Kiểm tra mobile view

### Bước 5: Demo Save & Reset (1 phút)

#### Save Configuration:
1. Click button "Lưu cấu hình"
2. Xem toast notification với JSON config
3. Giải thích cách data sẽ được gửi đến backend

#### Reset to Defaults:
1. Click button "Đặt lại"
2. Quan sát tất cả thay đổi được reset
3. Màu sắc và CSS về mặc định

### Bước 6: Demo Advanced Features (1 phút)

#### CSS Variables Usage:
1. Mở DevTools (F12)
2. Inspect element và xem CSS variables
3. Giải thích cách `var(--domain-primary)` hoạt động

#### CSS Classes:
1. Inspect demo elements
2. Xem các classes như `.domain-bg-primary`, `.domain-border-accent`
3. Giải thích utility classes system

### Bước 7: Production Integration (30 giây)

#### Giải thích cách tích hợp:
1. **Backend**: Domain model với `theme_colors` và `custom_css` fields
2. **Frontend**: DomainContext áp dụng styles tự động
3. **API**: Endpoints để save/load domain configuration
4. **Middleware**: Domain resolution và config loading

## Key Points để nhấn mạnh:

### 1. Real-time Updates
- Tất cả thay đổi áp dụng ngay lập tức
- Không cần refresh page
- Live preview cho phép test nhanh

### 2. CSS Override System
- High specificity để override default styles
- CSS variables cho consistency
- Utility classes cho convenience

### 3. Multi-tenant Architecture
- Mỗi domain có config riêng
- Isolated styling per organization
- Scalable solution

### 4. Developer Experience
- Easy-to-use color picker
- JSON format cho CSS
- Comprehensive documentation
- Error handling và validation

## Demo Tips:

1. **Chuẩn bị trước**: Test tất cả features trước khi demo
2. **Explain while doing**: Giải thích từng bước khi thực hiện
3. **Show DevTools**: Để audience hiểu technical implementation
4. **Handle errors gracefully**: Nếu có lỗi, giải thích và fix
5. **Interactive**: Cho audience suggest màu sắc hoặc CSS để test

## Câu hỏi thường gặp:

**Q: CSS có conflict với existing styles không?**
A: Không, vì sử dụng high specificity và CSS variables

**Q: Performance impact như thế nào?**
A: Minimal, chỉ inject CSS một lần khi domain config load

**Q: Có support mobile không?**
A: Có, responsive design và mobile-friendly

**Q: Có thể undo changes không?**
A: Có button reset và có thể implement undo/redo

**Q: Security concerns?**
A: CSS được sanitize và validate trước khi áp dụng
