'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Textarea } from '@/components/ui/textarea'
import { isValidColor, hexToCustomProperty, applyThemeColors, applyCustomCSS } from '@/lib/utils/cssOverride'
import type { ThemeColors } from '@/lib/utils/cssOverride'

interface DomainColorPickerProps {
  initialColors?: Partial<ThemeColors>
  initialCustomCSS?: Record<string, any>
  onColorsChange?: (colors: ThemeColors) => void
  onCustomCSSChange?: (customCSS: Record<string, any>) => void
  showPreview?: boolean
}

const defaultColors: ThemeColors = {
  primary: '#3b82f6',
  secondary: '#64748b',
  accent: '#f59e0b',
  background: '#ffffff',
  surface: '#f8fafc',
  text: '#1e293b',
  text_secondary: '#64748b',
}

const colorLabels = {
  primary: 'Màu chính',
  secondary: 'Màu phụ',
  accent: 'Màu nhấn',
  background: 'Màu nền',
  surface: 'Màu bề mặt',
  text: 'Màu chữ chính',
  text_secondary: 'Màu chữ phụ',
}

export function DomainColorPicker({
  initialColors = {},
  initialCustomCSS = {},
  onColorsChange,
  onCustomCSSChange,
  showPreview = true,
}: DomainColorPickerProps): React.JSX.Element {
  const [colors, setColors] = useState<ThemeColors>({
    ...defaultColors,
    ...initialColors,
  })
  
  const [customCSS, setCustomCSS] = useState<string>(
    JSON.stringify(initialCustomCSS, null, 2)
  )
  
  const [cssError, setCSSError] = useState<string>('')
  const [previewMode, setPreviewMode] = useState<boolean>(false)

  // Handle color changes
  const handleColorChange = (key: keyof ThemeColors, value: string): void => {
    if (isValidColor(value)) {
      const newColors = { ...colors, [key]: value }
      setColors(newColors)
      onColorsChange?.(newColors)
      
      // Apply preview if enabled
      if (previewMode && showPreview) {
        applyThemeColors(newColors)
      }
    }
  }

  // Handle custom CSS changes
  const handleCustomCSSChange = (value: string): void => {
    setCustomCSS(value)
    setCSSError('')
    
    try {
      const parsed = JSON.parse(value)
      onCustomCSSChange?.(parsed)
      
      // Apply preview if enabled
      if (previewMode && showPreview) {
        applyCustomCSS(parsed)
      }
    } catch (error) {
      setCSSError('JSON không hợp lệ')
    }
  }

  // Toggle preview mode
  const togglePreview = (): void => {
    setPreviewMode(!previewMode)
    
    if (!previewMode && showPreview) {
      // Apply current settings
      applyThemeColors(colors)
      try {
        const parsed = JSON.parse(customCSS)
        applyCustomCSS(parsed)
      } catch (error) {
        // Ignore CSS errors in preview
      }
    }
  }

  // Reset to defaults
  const resetToDefaults = (): void => {
    setColors(defaultColors)
    setCustomCSS('{}')
    onColorsChange?.(defaultColors)
    onCustomCSSChange?.({})
    
    if (previewMode && showPreview) {
      applyThemeColors(defaultColors)
      applyCustomCSS({})
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-medium">Tùy chỉnh giao diện</h3>
          <p className="text-sm text-muted-foreground">
            Cấu hình màu sắc và CSS tùy chỉnh cho domain
          </p>
        </div>
        
        {showPreview && (
          <div className="flex gap-2">
            <Button
              variant={previewMode ? 'default' : 'outline'}
              size="sm"
              onClick={togglePreview}
            >
              {previewMode ? 'Đang xem trước' : 'Xem trước'}
            </Button>
            <Button variant="outline" size="sm" onClick={resetToDefaults}>
              Đặt lại mặc định
            </Button>
          </div>
        )}
      </div>

      <Tabs defaultValue="colors" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="colors">Màu sắc</TabsTrigger>
          <TabsTrigger value="css">CSS tùy chỉnh</TabsTrigger>
        </TabsList>
        
        <TabsContent value="colors" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Bảng màu chủ đề</CardTitle>
              <CardDescription>
                Chọn màu sắc cho các thành phần chính của giao diện
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(colors).map(([key, value]) => (
                  <div key={key} className="space-y-2">
                    <Label htmlFor={key}>{colorLabels[key as keyof ThemeColors]}</Label>
                    <div className="flex gap-2">
                      <Input
                        id={key}
                        type="color"
                        value={value}
                        onChange={(e) => handleColorChange(key as keyof ThemeColors, e.target.value)}
                        className="w-16 h-10 p-1 border rounded"
                      />
                      <Input
                        type="text"
                        value={value}
                        onChange={(e) => handleColorChange(key as keyof ThemeColors, e.target.value)}
                        placeholder="#000000"
                        className="flex-1"
                      />
                    </div>
                  </div>
                ))}
              </div>
              
              {previewMode && (
                <div className="mt-4 p-4 bg-muted rounded-lg">
                  <p className="text-sm text-muted-foreground mb-2">Xem trước màu sắc:</p>
                  <div className="flex flex-wrap gap-2">
                    {Object.entries(colors).map(([key, value]) => (
                      <Badge key={key} variant="outline" className="flex items-center gap-2">
                        <div 
                          className="w-3 h-3 rounded-full border" 
                          style={{ backgroundColor: value }}
                        />
                        {colorLabels[key as keyof ThemeColors]}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="css" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>CSS tùy chỉnh</CardTitle>
              <CardDescription>
                Thêm CSS tùy chỉnh để ghi đè các style mặc định. Sử dụng định dạng JSON.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="custom-css">CSS Rules (JSON format)</Label>
                <Textarea
                  id="custom-css"
                  value={customCSS}
                  onChange={(e) => handleCustomCSSChange(e.target.value)}
                  placeholder={`{
  ".btn-primary": {
    "background-color": "#ff0000",
    "border-color": "#ff0000"
  },
  ".navbar": {
    "background-color": "var(--domain-primary)"
  }
}`}
                  className="min-h-[200px] font-mono text-sm"
                />
                {cssError && (
                  <p className="text-sm text-destructive">{cssError}</p>
                )}
              </div>
              
              <div className="text-xs text-muted-foreground space-y-1">
                <p><strong>Lưu ý:</strong></p>
                <ul className="list-disc list-inside space-y-1">
                  <li>CSS sẽ được áp dụng với độ ưu tiên cao (!important)</li>
                  <li>Có thể sử dụng CSS variables: var(--domain-primary), var(--domain-secondary), etc.</li>
                  <li>Selector sẽ được tăng độ ưu tiên tự động để ghi đè CSS mặc định</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
