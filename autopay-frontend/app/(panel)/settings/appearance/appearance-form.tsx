'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { ChevronDownIcon } from '@radix-ui/react-icons'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { DomainColorPicker } from '@/components/domain/domain-color-picker'
import { Button, buttonVariants } from '@/components/ui/button'
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { Separator } from '@/components/ui/separator'
import { toast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import type { ThemeColors } from '@/lib/utils/cssOverride'
import { useState } from 'react'

const appearanceFormSchema = z.object({
  theme: z.enum(['light', 'dark'], {
    required_error: 'Please select a theme.',
  }),
  font: z.enum(['inter', 'manrope', 'system'], {
    invalid_type_error: 'Select a font',
    required_error: 'Please select a font.',
  }),
})

type AppearanceFormValues = z.infer<typeof appearanceFormSchema>

// This can come from your database or API.
const defaultValues: Partial<AppearanceFormValues> = {
  theme: 'light',
}

export function AppearanceForm() {
  const form = useForm<AppearanceFormValues>({
    resolver: zodResolver(appearanceFormSchema),
    defaultValues,
  })

  // State for domain customization demo
  const [domainColors, setDomainColors] = useState<ThemeColors>({
    primary: '#3b82f6',
    secondary: '#64748b',
    accent: '#f59e0b',
    background: '#ffffff',
    surface: '#f8fafc',
    text: '#1e293b',
    text_secondary: '#64748b',
  })

  const [customCSS, setCustomCSS] = useState<Record<string, any>>({
    '.demo-button': {
      'border-radius': '12px',
      'font-weight': '600',
    },
    '.demo-card': {
      border: '2px solid var(--domain-accent)',
      'box-shadow': '0 4px 6px rgba(0, 0, 0, 0.1)',
    },
  })

  function onSubmit(data: AppearanceFormValues) {
    toast({
      title: 'Cập nhật cấu hình giao diện:',
      description: (
        <pre className="mt-2 w-[340px] rounded-md bg-slate-950 p-4">
          <code className="text-white">{JSON.stringify(data, null, 2)}</code>
        </pre>
      ),
    })
  }

  const handleColorsChange = (colors: ThemeColors) => {
    setDomainColors(colors)
    toast({
      title: 'Màu sắc đã được cập nhật',
      description: 'Thay đổi màu sắc sẽ được áp dụng ngay lập tức.',
    })
  }

  const handleCustomCSSChange = (css: Record<string, any>) => {
    setCustomCSS(css)
    toast({
      title: 'CSS tùy chỉnh đã được cập nhật',
      description: 'Thay đổi CSS sẽ được áp dụng ngay lập tức.',
    })
  }

  return (
    <div className="space-y-8">
      {/* Domain Customization Section */}
      <div className="space-y-4">
        <div>
          <h3 className="text-lg font-medium">Tùy chỉnh Domain</h3>
          <p className="text-muted-foreground text-sm">
            Demo tính năng tùy chỉnh màu sắc và CSS cho domain. Thay đổi sẽ được áp dụng ngay lập tức.
          </p>
        </div>

        <DomainColorPicker
          initialColors={domainColors}
          initialCustomCSS={customCSS}
          onColorsChange={handleColorsChange}
          onCustomCSSChange={handleCustomCSSChange}
          showPreview={true}
        />
      </div>

      <Separator />

      {/* Demo Elements */}
      <div className="space-y-4">
        <div>
          <h3 className="text-lg font-medium">Demo Elements</h3>
          <p className="text-muted-foreground text-sm">
            Các element này sẽ thay đổi theo cấu hình màu sắc và CSS bên trên.
          </p>
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          {/* Demo Buttons */}
          <div className="demo-card space-y-3 rounded-lg border p-4">
            <h4 className="font-medium">Buttons</h4>
            <div className="flex flex-wrap gap-2">
              <Button className="demo-button domain-bg-primary">Primary Button</Button>
              <Button
                variant="outline"
                className="demo-button domain-border-primary domain-primary">
                Outline Button
              </Button>
              <Button
                variant="secondary"
                className="demo-button domain-bg-secondary">
                Secondary Button
              </Button>
            </div>
          </div>

          {/* Demo Colors */}
          <div className="demo-card space-y-3 rounded-lg border p-4">
            <h4 className="font-medium">Color Palette</h4>
            <div className="grid grid-cols-4 gap-2">
              <div className="space-y-1">
                <div className="domain-bg-primary h-8 w-full rounded"></div>
                <p className="text-center text-xs">Primary</p>
              </div>
              <div className="space-y-1">
                <div className="domain-bg-secondary h-8 w-full rounded"></div>
                <p className="text-center text-xs">Secondary</p>
              </div>
              <div className="space-y-1">
                <div className="domain-bg-accent h-8 w-full rounded"></div>
                <p className="text-center text-xs">Accent</p>
              </div>
              <div className="space-y-1">
                <div className="bg-muted h-8 w-full rounded"></div>
                <p className="text-center text-xs">Muted</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Separator />

      {/* Original Theme Settings */}
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="space-y-8">
          <div>
            <h3 className="text-lg font-medium">Cấu hình hệ thống</h3>
            <p className="text-muted-foreground text-sm">Cấu hình theme và font cho hệ thống.</p>
          </div>

          <FormField
            control={form.control}
            name="font"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Font</FormLabel>
                <div className="relative w-max">
                  <FormControl>
                    <select
                      className={cn(buttonVariants({ variant: 'outline' }), 'w-[200px] appearance-none font-normal')}
                      {...field}>
                      <option value="inter">Inter</option>
                      <option value="manrope">Manrope</option>
                      <option value="system">System</option>
                    </select>
                  </FormControl>
                  <ChevronDownIcon className="absolute top-2.5 right-3 h-4 w-4 opacity-50" />
                </div>
                <FormDescription>Set the font you want to use in the dashboard.</FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="theme"
            render={({ field }) => (
              <FormItem className="space-y-1">
                <FormLabel>Theme</FormLabel>
                <FormDescription>Select the theme for the dashboard.</FormDescription>
                <FormMessage />
                <RadioGroup
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  className="grid max-w-md grid-cols-2 gap-8 pt-2">
                  <FormItem>
                    <FormLabel className="[&:has([data-state=checked])>div]:border-primary">
                      <FormControl>
                        <RadioGroupItem
                          value="light"
                          className="sr-only"
                        />
                      </FormControl>
                      <div className="border-muted hover:border-accent items-center rounded-md border-2 p-1">
                        <div className="space-y-2 rounded-sm bg-[#ecedef] p-2">
                          <div className="space-y-2 rounded-md bg-white p-2 shadow-xs">
                            <div className="h-2 w-[80px] rounded-lg bg-[#ecedef]" />
                            <div className="h-2 w-[100px] rounded-lg bg-[#ecedef]" />
                          </div>
                          <div className="flex items-center space-x-2 rounded-md bg-white p-2 shadow-xs">
                            <div className="h-4 w-4 rounded-full bg-[#ecedef]" />
                            <div className="h-2 w-[100px] rounded-lg bg-[#ecedef]" />
                          </div>
                          <div className="flex items-center space-x-2 rounded-md bg-white p-2 shadow-xs">
                            <div className="h-4 w-4 rounded-full bg-[#ecedef]" />
                            <div className="h-2 w-[100px] rounded-lg bg-[#ecedef]" />
                          </div>
                        </div>
                      </div>
                      <span className="block w-full p-2 text-center font-normal">Light</span>
                    </FormLabel>
                  </FormItem>
                  <FormItem>
                    <FormLabel className="[&:has([data-state=checked])>div]:border-primary">
                      <FormControl>
                        <RadioGroupItem
                          value="dark"
                          className="sr-only"
                        />
                      </FormControl>
                      <div className="border-muted bg-popover hover:bg-accent hover:text-accent-foreground items-center rounded-md border-2 p-1">
                        <div className="space-y-2 rounded-sm bg-slate-950 p-2">
                          <div className="space-y-2 rounded-md bg-slate-800 p-2 shadow-xs">
                            <div className="h-2 w-[80px] rounded-lg bg-slate-400" />
                            <div className="h-2 w-[100px] rounded-lg bg-slate-400" />
                          </div>
                          <div className="flex items-center space-x-2 rounded-md bg-slate-800 p-2 shadow-xs">
                            <div className="h-4 w-4 rounded-full bg-slate-400" />
                            <div className="h-2 w-[100px] rounded-lg bg-slate-400" />
                          </div>
                          <div className="flex items-center space-x-2 rounded-md bg-slate-800 p-2 shadow-xs">
                            <div className="h-4 w-4 rounded-full bg-slate-400" />
                            <div className="h-2 w-[100px] rounded-lg bg-slate-400" />
                          </div>
                        </div>
                      </div>
                      <span className="block w-full p-2 text-center font-normal">Dark</span>
                    </FormLabel>
                  </FormItem>
                </RadioGroup>
              </FormItem>
            )}
          />

          <Button
            type="submit"
            size="sm">
            Update preferences
          </Button>
        </form>
      </Form>
    </div>
  )
}
