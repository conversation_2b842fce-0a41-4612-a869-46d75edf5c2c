'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { DomainColorPicker } from '@/components/domain/domain-color-picker'
import { toast } from '@/components/ui/use-toast'
import type { ThemeColors } from '@/lib/utils/cssOverride'
import { Palette, Eye, RotateCcw, Save } from 'lucide-react'

export default function DomainDemoPage() {
  const [domainColors, setDomainColors] = useState<ThemeColors>({
    primary: '#3b82f6',
    secondary: '#64748b',
    accent: '#f59e0b',
    background: '#ffffff',
    surface: '#f8fafc',
    text: '#1e293b',
    text_secondary: '#64748b',
  })

  const [customCSS, setCustomCSS] = useState<Record<string, any>>({
    '.demo-button': {
      'border-radius': '12px',
      'font-weight': '600',
      'text-transform': 'uppercase',
      'letter-spacing': '0.5px',
    },
    '.demo-card': {
      'border': '2px solid var(--domain-accent)',
      'box-shadow': '0 8px 16px rgba(0, 0, 0, 0.1)',
      'border-radius': '16px',
    },
    '.demo-navbar': {
      'background': 'linear-gradient(135deg, var(--domain-primary), var(--domain-secondary))',
      'border-radius': '12px',
      'padding': '1rem',
    },
    '.demo-badge': {
      'background-color': 'var(--domain-accent)',
      'color': 'white',
      'border-radius': '20px',
    },
  })

  const handleColorsChange = (colors: ThemeColors) => {
    setDomainColors(colors)
    toast({
      title: 'Màu sắc đã được cập nhật',
      description: 'Thay đổi màu sắc sẽ được áp dụng ngay lập tức.',
    })
  }

  const handleCustomCSSChange = (css: Record<string, any>) => {
    setCustomCSS(css)
    toast({
      title: 'CSS tùy chỉnh đã được cập nhật',
      description: 'Thay đổi CSS sẽ được áp dụng ngay lập tức.',
    })
  }

  const resetToDefaults = () => {
    const defaultColors: ThemeColors = {
      primary: '#3b82f6',
      secondary: '#64748b',
      accent: '#f59e0b',
      background: '#ffffff',
      surface: '#f8fafc',
      text: '#1e293b',
      text_secondary: '#64748b',
    }
    setDomainColors(defaultColors)
    setCustomCSS({})
    toast({
      title: 'Đã đặt lại về mặc định',
      description: 'Tất cả cấu hình đã được đặt lại về giá trị mặc định.',
    })
  }

  const saveConfiguration = () => {
    // Simulate API call
    toast({
      title: 'Lưu cấu hình thành công',
      description: (
        <div className="space-y-2">
          <p>Cấu hình domain đã được lưu:</p>
          <pre className="text-xs bg-muted p-2 rounded">
            {JSON.stringify({ theme_colors: domainColors, custom_css: customCSS }, null, 2)}
          </pre>
        </div>
      ),
    })
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <Palette className="h-6 w-6" />
            Demo Domain Customization
          </h1>
          <p className="text-muted-foreground">
            Test và demo tính năng tùy chỉnh giao diện cho domain. Thay đổi sẽ được áp dụng ngay lập tức.
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline" onClick={resetToDefaults}>
            <RotateCcw className="mr-2 h-4 w-4" />
            Đặt lại
          </Button>
          <Button onClick={saveConfiguration}>
            <Save className="mr-2 h-4 w-4" />
            Lưu cấu hình
          </Button>
        </div>
      </div>

      <Separator />

      {/* Domain Color Picker */}
      <DomainColorPicker
        initialColors={domainColors}
        initialCustomCSS={customCSS}
        onColorsChange={handleColorsChange}
        onCustomCSSChange={handleCustomCSSChange}
        showPreview={true}
      />

      <Separator />

      {/* Demo Section */}
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Eye className="h-5 w-5" />
          <h2 className="text-xl font-semibold">Demo Elements</h2>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Demo Navbar */}
          <Card className="demo-card">
            <CardHeader>
              <CardTitle>Navigation Bar</CardTitle>
              <CardDescription>Demo navbar với gradient background</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="demo-navbar text-white">
                <div className="flex items-center justify-between">
                  <h3 className="font-bold">Brand Name</h3>
                  <div className="flex gap-2">
                    <Badge className="demo-badge">New</Badge>
                    <Badge variant="outline" className="text-white border-white">Pro</Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Demo Buttons */}
          <Card className="demo-card">
            <CardHeader>
              <CardTitle>Buttons</CardTitle>
              <CardDescription>Các loại button với custom styling</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-wrap gap-2">
                <Button className="demo-button domain-bg-primary">Primary Action</Button>
                <Button variant="outline" className="demo-button domain-border-primary domain-primary">
                  Secondary Action
                </Button>
                <Button variant="ghost" className="demo-button domain-primary">
                  Ghost Button
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                <Button size="sm" className="demo-button domain-bg-accent">Small Button</Button>
                <Button size="lg" className="demo-button domain-bg-secondary">Large Button</Button>
              </div>
            </CardContent>
          </Card>

          {/* Demo Form */}
          <Card className="demo-card">
            <CardHeader>
              <CardTitle>Form Elements</CardTitle>
              <CardDescription>Input và form controls</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="demo-input">Email Address</Label>
                <Input 
                  id="demo-input" 
                  placeholder="Enter your email" 
                  className="domain-border-primary focus:domain-border-accent"
                />
              </div>
              <Button className="w-full demo-button domain-bg-primary">
                Subscribe
              </Button>
            </CardContent>
          </Card>

          {/* Demo Color Palette */}
          <Card className="demo-card">
            <CardHeader>
              <CardTitle>Color Palette</CardTitle>
              <CardDescription>Bảng màu hiện tại của domain</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-4 gap-3">
                <div className="text-center space-y-2">
                  <div className="w-full h-12 rounded-lg domain-bg-primary"></div>
                  <p className="text-xs font-medium">Primary</p>
                  <p className="text-xs text-muted-foreground">{domainColors.primary}</p>
                </div>
                <div className="text-center space-y-2">
                  <div className="w-full h-12 rounded-lg domain-bg-secondary"></div>
                  <p className="text-xs font-medium">Secondary</p>
                  <p className="text-xs text-muted-foreground">{domainColors.secondary}</p>
                </div>
                <div className="text-center space-y-2">
                  <div className="w-full h-12 rounded-lg domain-bg-accent"></div>
                  <p className="text-xs font-medium">Accent</p>
                  <p className="text-xs text-muted-foreground">{domainColors.accent}</p>
                </div>
                <div className="text-center space-y-2">
                  <div className="w-full h-12 rounded-lg bg-muted"></div>
                  <p className="text-xs font-medium">Muted</p>
                  <p className="text-xs text-muted-foreground">System</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Demo Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="demo-card">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">Card {i}</CardTitle>
                  <Badge className="demo-badge">Featured</Badge>
                </div>
                <CardDescription>
                  Demo card với custom styling và màu sắc domain
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="h-2 domain-bg-primary rounded-full w-3/4"></div>
                  <div className="h-2 domain-bg-secondary rounded-full w-1/2"></div>
                  <Button size="sm" className="demo-button domain-bg-accent w-full">
                    Action {i}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}
