/* modal padding */
.bg-card-gradient {
  background-image:
    linear-gradient(
      16deg,
      rgba(116, 116, 116, 0.02) 0%,
      rgba(116, 116, 116, 0.02) 25%,
      transparent 25%,
      transparent 96%,
      rgba(177, 177, 177, 0.02) 96%,
      rgba(177, 177, 177, 0.02) 100%
    ),
    linear-gradient(
      236deg,
      rgba(148, 148, 148, 0.02) 0%,
      rgba(148, 148, 148, 0.02) 53%,
      transparent 53%,
      transparent 59%,
      rgba(56, 56, 56, 0.02) 59%,
      rgba(56, 56, 56, 0.02) 100%
    ),
    linear-gradient(
      284deg,
      rgba(16, 16, 16, 0.02) 0%,
      rgba(16, 16, 16, 0.02) 46%,
      transparent 46%,
      transparent 71%,
      rgba(181, 181, 181, 0.02) 71%,
      rgba(181, 181, 181, 0.02) 100%
    ),
    linear-gradient(
      316deg,
      rgba(197, 197, 197, 0.02) 0%,
      rgba(197, 197, 197, 0.02) 26%,
      transparent 26%,
      transparent 49%,
      rgba(58, 58, 58, 0.02) 49%,
      rgba(58, 58, 58, 0.02) 100%
    ),
    linear-gradient(90deg, rgb(255, 255, 255), rgb(255, 255, 255));
}

body {
  @apply min-h-screen bg-cover bg-no-repeat;
  /*background-image: radial-gradient(circle at 13% 47%, rgba(140, 140, 140,0.03) 0%, rgba(140, 140, 140,0.03) 25%,transparent 25%, transparent 100%),radial-gradient(circle at 28% 63%, rgba(143, 143, 143,0.03) 0%, rgba(143, 143, 143,0.03) 16%,transparent 16%, transparent 100%),radial-gradient(circle at 81% 56%, rgba(65, 65, 65,0.03) 0%, rgba(65, 65, 65,0.03) 12%,transparent 12%, transparent 100%),radial-gradient(circle at 26% 48%, rgba(60, 60, 60,0.03) 0%, rgba(60, 60, 60,0.03) 6%,transparent 6%, transparent 100%),radial-gradient(circle at 97% 17%, rgba(150, 150, 150,0.03) 0%, rgba(150, 150, 150,0.03) 56%,transparent 56%, transparent 100%),radial-gradient(circle at 50% 100%, rgba(25, 25, 25,0.03) 0%, rgba(25, 25, 25,0.03) 36%,transparent 36%, transparent 100%),radial-gradient(circle at 55% 52%, rgba(69, 69, 69,0.03) 0%, rgba(69, 69, 69,0.03) 6%,transparent 6%, transparent 100%),linear-gradient(90deg, rgb(255,255,255),rgb(255,255,255));*/
  background-image:
    radial-gradient(
      circle at 57% 36%,
      hsla(263, 0%, 78%, 0.04) 0%,
      hsla(263, 0%, 78%, 0.04) 10%,
      transparent 10%,
      transparent 100%
    ),
    radial-gradient(
      circle at 22% 61%,
      hsla(263, 0%, 78%, 0.04) 0%,
      hsla(263, 0%, 78%, 0.04) 36%,
      transparent 36%,
      transparent 100%
    ),
    radial-gradient(
      circle at 68% 97%,
      hsla(263, 0%, 78%, 0.04) 0%,
      hsla(263, 0%, 78%, 0.04) 41%,
      transparent 41%,
      transparent 100%
    ),
    radial-gradient(
      circle at 57% 89%,
      hsla(263, 0%, 78%, 0.04) 0%,
      hsla(263, 0%, 78%, 0.04) 30%,
      transparent 30%,
      transparent 100%
    ),
    radial-gradient(
      circle at 39% 80%,
      hsla(263, 0%, 78%, 0.04) 0%,
      hsla(263, 0%, 78%, 0.04) 22%,
      transparent 22%,
      transparent 100%
    ),
    radial-gradient(
      circle at 88% 71%,
      hsla(263, 0%, 78%, 0.04) 0%,
      hsla(263, 0%, 78%, 0.04) 30%,
      transparent 30%,
      transparent 100%
    ),
    linear-gradient(0deg, rgb(255, 255, 255), rgb(255, 255, 255));
  /*background-image: radial-gradient(circle at 77% 66%, hsla(39,0%,85%,0.05) 0%, hsla(39,0%,85%,0.05) 9%,transparent 9%, transparent 43%,transparent 43%, transparent 100%),radial-gradient(circle at 6% 56%, hsla(39,0%,85%,0.05) 0%, hsla(39,0%,85%,0.05) 20%,transparent 20%, transparent 56%,transparent 56%, transparent 100%),radial-gradient(circle at 48% 45%, hsla(39,0%,85%,0.05) 0%, hsla(39,0%,85%,0.05) 15%,transparent 15%, transparent 85%,transparent 85%, transparent 100%),radial-gradient(circle at 89% 6%, hsla(39,0%,85%,0.05) 0%, hsla(39,0%,85%,0.05) 5%,transparent 5%, transparent 32%,transparent 32%, transparent 100%),radial-gradient(circle at 71% 38%, hsla(39,0%,85%,0.05) 0%, hsla(39,0%,85%,0.05) 14%,transparent 14%, transparent 21%,transparent 21%, transparent 100%),radial-gradient(circle at 84% 78%, hsla(39,0%,85%,0.05) 0%, hsla(39,0%,85%,0.05) 11%,transparent 11%, transparent 85%,transparent 85%, transparent 100%),radial-gradient(circle at 92% 72%, hsla(39,0%,85%,0.05) 0%, hsla(39,0%,85%,0.05) 42%,transparent 42%, transparent 51%,transparent 51%, transparent 100%),radial-gradient(circle at 73% 95%, hsla(39,0%,85%,0.05) 0%, hsla(39,0%,85%,0.05) 48%,transparent 48%, transparent 63%,transparent 63%, transparent 100%),radial-gradient(circle at 29% 29%, hsla(39,0%,85%,0.05) 0%, hsla(39,0%,85%,0.05) 24%,transparent 24%, transparent 73%,transparent 73%, transparent 100%),linear-gradient(90deg, hsl(147,0%,99%),hsl(147,0%,99%));*/

  @variant dark {
    background-image:
      radial-gradient(
        circle at 84% 82%,
        rgba(217, 217, 217, 0.03) 0%,
        rgba(217, 217, 217, 0.03) 21%,
        transparent 21%,
        transparent 100%
      ),
      radial-gradient(
        circle at 75% 56%,
        rgba(3, 3, 3, 0.03) 0%,
        rgba(3, 3, 3, 0.03) 30%,
        transparent 30%,
        transparent 100%
      ),
      radial-gradient(
        circle at 74% 53%,
        rgba(153, 153, 153, 0.03) 0%,
        rgba(153, 153, 153, 0.03) 95%,
        transparent 95%,
        transparent 100%
      ),
      radial-gradient(
        circle at 86% 43%,
        rgba(209, 209, 209, 0.03) 0%,
        rgba(209, 209, 209, 0.03) 83%,
        transparent 83%,
        transparent 100%
      ),
      radial-gradient(
        circle at 64% 88%,
        rgba(192, 192, 192, 0.03) 0%,
        rgba(192, 192, 192, 0.03) 2%,
        transparent 2%,
        transparent 100%
      ),
      radial-gradient(
        circle at 73% 77%,
        rgba(205, 205, 205, 0.03) 0%,
        rgba(205, 205, 205, 0.03) 18%,
        transparent 18%,
        transparent 100%
      ),
      radial-gradient(
        circle at 57% 51%,
        rgba(161, 161, 161, 0.03) 0%,
        rgba(161, 161, 161, 0.03) 64%,
        transparent 64%,
        transparent 100%
      ),
      radial-gradient(
        circle at 40% 84%,
        rgba(115, 115, 115, 0.03) 0%,
        rgba(115, 115, 115, 0.03) 14%,
        transparent 14%,
        transparent 100%
      ),
      linear-gradient(90deg, rgb(0, 0, 0), rgb(0, 0, 0));
    /*background-image: radial-gradient(circle at 11% 37%, hsla(251,0%,28%,0.05) 0%, hsla(251,0%,28%,0.05) 50%,transparent 50%, transparent 56%,transparent 56%, transparent 100%),radial-gradient(circle at 82% 7%, hsla(251,0%,28%,0.05) 0%, hsla(251,0%,28%,0.05) 46%,transparent 46%, transparent 88%,transparent 88%, transparent 100%),radial-gradient(circle at 81% 79%, hsla(251,0%,28%,0.05) 0%, hsla(251,0%,28%,0.05) 33%,transparent 33%, transparent 89%,transparent 89%, transparent 100%),radial-gradient(circle at 68% 96%, hsla(251,0%,28%,0.05) 0%, hsla(251,0%,28%,0.05) 8%,transparent 8%, transparent 26%,transparent 26%, transparent 100%),radial-gradient(circle at 69% 20%, hsla(251,0%,28%,0.05) 0%, hsla(251,0%,28%,0.05) 84%,transparent 84%, transparent 86%,transparent 86%, transparent 100%),radial-gradient(circle at 49% 22%, hsla(251,0%,28%,0.05) 0%, hsla(251,0%,28%,0.05) 71%,transparent 71%, transparent 78%,transparent 78%, transparent 100%),radial-gradient(circle at 23% 60%, hsla(251,0%,28%,0.05) 0%, hsla(251,0%,28%,0.05) 6%,transparent 6%, transparent 40%,transparent 40%, transparent 100%),radial-gradient(circle at 86% 33%, hsla(251,0%,28%,0.05) 0%, hsla(251,0%,28%,0.05) 13%,transparent 13%, transparent 98%,transparent 98%, transparent 100%),radial-gradient(circle at 38% 60%, hsla(251,0%,28%,0.05) 0%, hsla(251,0%,28%,0.05) 15%,transparent 15%, transparent 61%,transparent 61%, transparent 100%),linear-gradient(0deg, hsl(167,0%,6%),hsl(167,0%,6%));*/
    /*background-image: radial-gradient(circle at 16% 83%, rgba(148, 148, 148,0.06) 0%, rgba(148, 148, 148,0.06) 50%,rgba(63, 63, 63,0.06) 50%, rgba(63, 63, 63,0.06) 100%),radial-gradient(circle at 68% 87%, rgba(66, 66, 66,0.06) 0%, rgba(66, 66, 66,0.06) 50%,rgba(105, 105, 105,0.06) 50%, rgba(105, 105, 105,0.06) 100%),radial-gradient(circle at 38% 50%, rgba(123, 123, 123,0.06) 0%, rgba(123, 123, 123,0.06) 50%,rgba(172, 172, 172,0.06) 50%, rgba(172, 172, 172,0.06) 100%),linear-gradient(90deg, hsl(18,0%,1%),hsl(18,0%,1%));*/
  }
}

@keyframes wave {
  0%,
  100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(15deg);
  }
  75% {
    transform: rotate(-15deg);
  }
}
.animation-wave {
  animation: wave 0.5s ease-in-out infinite;
}

/* ScrollArea horizontal scrolling improvements */
[data-radix-scroll-area-viewport] {
  -webkit-overflow-scrolling: touch; /* iOS momentum scrolling */
  scroll-behavior: smooth; /* Smooth scrolling */
}

/* Ensure horizontal scrolling works on mobile */
[data-radix-scroll-area-viewport][data-orientation='horizontal'] {
  touch-action: pan-x; /* Allow horizontal panning only */
}

/* Prevent nested ScrollArea from affecting parent layout */
[data-radix-scroll-area-root] {
  max-width: 100%;
  min-width: 0;
}

/* Ensure horizontal ScrollArea viewport doesn't expand */
[data-radix-scroll-area-viewport][data-orientation='horizontal'] {
  max-width: 100%;
  overflow-x: auto;
}

/* Horizontal scrolling for categories */
.overflow-x-auto {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.overflow-x-auto::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

/* Ensure inline-flex doesn't break container width */
.inline-flex {
  white-space: nowrap;
}

/* Force ScrollArea to respect container width */
[data-radix-scroll-area-root] {
  width: 100% !important;
  max-width: 100% !important;
  overflow: hidden !important;
}

/* Ensure ScrollArea viewport respects parent width */
[data-radix-scroll-area-viewport] {
  width: 100% !important;
  max-width: 100% !important;
}

/* Domain-specific CSS override utilities */
body[data-domain-custom] {
  /* Higher specificity for domain customizations */
}

/* Domain theme color utilities */
.domain-primary {
  color: var(--domain-primary, var(--primary)) !important;
}

.domain-bg-primary {
  background-color: var(--domain-primary, var(--primary)) !important;
}

.domain-border-primary {
  border-color: var(--domain-primary, var(--primary)) !important;
}

.domain-secondary {
  color: var(--domain-secondary, var(--secondary)) !important;
}

.domain-bg-secondary {
  background-color: var(--domain-secondary, var(--secondary)) !important;
}

.domain-border-secondary {
  border-color: var(--domain-secondary, var(--secondary)) !important;
}

.domain-accent {
  color: var(--domain-accent, var(--accent)) !important;
}

.domain-bg-accent {
  background-color: var(--domain-accent, var(--accent)) !important;
}

.domain-border-accent {
  border-color: var(--domain-accent, var(--accent)) !important;
}

/* Override default button styles with domain colors */
body[data-domain-custom] .btn-primary,
body[data-domain-custom] .bg-primary {
  background-color: var(--domain-primary, var(--primary)) !important;
  border-color: var(--domain-primary, var(--primary)) !important;
}

body[data-domain-custom] .text-primary {
  color: var(--domain-primary, var(--primary)) !important;
}

body[data-domain-custom] .border-primary {
  border-color: var(--domain-primary, var(--primary)) !important;
}

/* Override Tailwind utilities with domain colors */
body[data-domain-custom] .bg-blue-600,
body[data-domain-custom] .bg-blue-500 {
  background-color: var(--domain-primary, var(--primary)) !important;
}

body[data-domain-custom] .text-blue-600,
body[data-domain-custom] .text-blue-500 {
  color: var(--domain-primary, var(--primary)) !important;
}

body[data-domain-custom] .border-blue-600,
body[data-domain-custom] .border-blue-500 {
  border-color: var(--domain-primary, var(--primary)) !important;
}

/* Ensure domain custom styles have highest priority */
body[data-domain-custom] [data-domain-override] {
  /* This selector provides maximum specificity for domain overrides */
}
